{"name": "blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "NODE_OPTIONS='--no-deprecation' jest"}, "dependencies": {"@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "bootstrap": "^5.3.5", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "react-redux": "^9.2.0", "react-router": "^7.6.0", "redax": "^0.0.0"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.26.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bootstrap": "^5.2.10", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.4", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "react-test-renderer": "^19.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}