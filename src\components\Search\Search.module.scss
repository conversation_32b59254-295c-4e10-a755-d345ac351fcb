.searchBox {
    display: flex;
    align-items: center;
    background-color: #0d6efd;
    border: 1px solid #4b7eff;
    height: 60px;
    // transition: width 0.5s ease;
}

.input {
    flex: 1;
    height: 100%;
    border: none;
    outline: none;
    padding: 0 20px;
    background-color: transparent;
    color: white;
    font-size: 16px;

    &::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }
}

.button {
    width: 60px;
    height: 100%;
    background-color: #0d6efd;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.icon {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}