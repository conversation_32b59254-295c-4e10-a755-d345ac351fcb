.menuToggle {
    width: 60px;
    height: 60px;
    cursor: pointer;
    z-index: 2;
    padding: 15px;
    border: 1px solid #4b7eff;


    img {
        width: 25px;
        height: 25px;
        object-fit: cover;
        object-position: center;
        filter: brightness(0) invert(1);
    }
}

.sidebar {
    margin-top: 60px;
    position: fixed;
    top: 0;
    left: -270px;
    width: 270px;
    height: 92vh;
    background-color: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    transition: left 0.3s ease;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.open {
    left: 0;
}

.topBar {
    background-color: #3870fe;
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.profile {
    display: flex;
    align-items: center;
    gap: 10px;
}

.avatar {
    width: 40px;
    height: 40px;
    background-color: #7aa0fe;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-weight: bold;
}

.username {
    font-weight: 600;
}

.navBlock {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.navBlock a {
    padding: 15px 20px;
    text-decoration: none;
    color: black;
    background: white;
    font-weight: 500;
    border-bottom: 1px solid #eee;
}

.navLinkActive {
    color: #1a22b2;
    font-weight: bold;
}

.navBlock a:hover {
    background-color: #f5f5f5;
}

.bottom {
    margin-top: auto;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
}

.themeSwitch {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    padding: 10px 0;
}

.themeBtn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px 50px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.themeBtn img {
    width: 20px;
    height: 20px;
}

.themeBtn:hover {
    background-color: #f0f0f0;
}

.logout {
    margin-top: 10px;
    background: #eee;
    border: none;
    width: 100%;
    padding: 15px;
    font-weight: bold;
    cursor: pointer;
}