.card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  
    .image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  
    .content {
      padding: 1rem;
    }
  
    .title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
  
    .text {
      font-size: 0.95rem;
      color: #555;
    }
  
    .row {
      display: flex;
      flex-direction: row;
      gap: 1rem;
    }
  }
  
  .large {
    flex-direction: row;
  
    .imageWrap {
      flex: 1;
    }
  
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
  
  .medium {
    .image {
      height: 200px;
    }
  }
  
  .small {
    .row {
      gap: 0.75rem;
    }
  
    .thumb {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
    }
  
    .content {
      padding: 0.5rem 0;
      display: flex;
      align-items: center;
    }
  
    .title {
      font-size: 0.95rem;
      font-weight: 500;
    }
  }
  